/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'class',
    path: '/class',
    component: 'layout.base$view.class',
    meta: {
      title: 'class',
      i18nKey: 'route.class',
      icon: 'mingcute:classify-2-line',
      order: 12,
      keepAlive: true,
      roles: ['R_ADMIN', 'R_AUDITOR']
    }
  },
  {
    name: 'classdetail',
    path: '/classdetail',
    component: 'layout.base$view.classdetail',
    meta: {
      title: 'classdetail',
      i18nKey: 'route.classdetail',
      icon: 'mingcute:classify-2-line',
      order: 12,
      keepAlive: true,
      hideInMenu: true,
      roles: ['R_ADMIN', 'R_AUDITOR']
    }
  },
  {
    name: 'content',
    path: '/content',
    component: 'layout.base',
    meta: {
      title: 'content',
      i18nKey: 'route.content',
      icon: 'bx:book-content',
      order: 14,
      roles: ['R_ADMIN', 'R_TEACHER']
    },
    children: [
      {
        name: 'content_test-course',
        path: '/content/test-course',
        component: 'view.content_test-course',
        meta: {
          title: 'content_test-course',
          i18nKey: 'route.content_test-course',
          icon: 'tdesign:course',
          keepAlive: true,
          roles: ['R_ADMIN']
        }
      },
      {
        name: 'content_test-course-pack',
        path: '/content/test-course-pack',
        component: 'view.content_test-course-pack',
        meta: {
          title: 'content_test-course-pack',
          i18nKey: 'route.content_test-course-pack',
          icon: 'material-symbols:package-2-outline',
          keepAlive: true,
          roles: ['R_ADMIN']
        }
      },
      {
        name: 'content_test-topic',
        path: '/content/test-topic',
        component: 'view.content_test-topic',
        meta: {
          title: 'content_test-topic',
          i18nKey: 'route.content_test-topic',
          icon: 'clarity:library-line',
          keepAlive: true,
          roles: ['R_ADMIN']
        }
      }
    ]
  },
  {
    name: 'enroll',
    path: '/enroll',
    component: 'layout.base',
    meta: {
      title: 'enroll',
      i18nKey: 'route.enroll',
      icon: 'mdi:account-multiple-plus',
      order: 2,
      roles: ['R_ADMIN']
    },
    children: [
      {
        name: 'enroll_config',
        path: '/enroll/config',
        component: 'view.enroll_config',
        meta: {
          title: 'enroll_config',
          i18nKey: 'route.enroll_config',
          icon: 'fluent-emoji-high-contrast:blue-book',
          roles: ['R_ADMIN']
        }
      },
      {
        name: 'enroll_enrollment',
        path: '/enroll/enrollment',
        component: 'view.enroll_enrollment',
        meta: {
          title: 'enroll_enrollment',
          i18nKey: 'route.enroll_enrollment',
          icon: 'fluent:form-28-regular',
          roles: ['R_ADMIN']
        }
      },
      {
        name: 'enroll_record',
        path: '/enroll/record',
        component: 'view.enroll_record',
        meta: {
          title: 'enroll_record',
          i18nKey: 'route.enroll_record',
          icon: 'mdi:account-supervisor',
          roles: ['R_ADMIN']
        }
      },
      {
        name: 'enroll_record-detail',
        path: '/enroll/record-detail',
        component: 'view.enroll_record-detail',
        meta: {
          title: 'enroll_record-detail',
          i18nKey: 'route.enroll_record-detail',
          hideInMenu: true
        }
      }
    ]
  },
  {
    name: 'enrollment',
    path: '/enrollment',
    component: 'layout.base$view.enrollment',
    meta: {
      title: 'enrollment',
      i18nKey: 'route.enrollment',
      icon: 'hugeicons:student-card',
      order: 10,
      keepAlive: true,
      hideInMenu: true,
      roles: ['R_ADMIN', 'R_AUDITOR']
    }
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1,
      keepAlive: true
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'order',
    path: '/order',
    component: 'layout.base$view.order',
    meta: {
      title: 'order',
      i18nKey: 'route.order',
      icon: 'mdi:order-bool-ascending-variant',
      hideInMenu: false,
      order: 13,
      keepAlive: true,
      roles: ['R_ADMIN', 'R_AUDITOR']
    }
  },
  {
    name: 'student',
    path: '/student',
    component: 'layout.base$view.student',
    meta: {
      title: 'student',
      i18nKey: 'route.student',
      icon: 'hugeicons:student-card',
      order: 11,
      keepAlive: true,
      roles: ['R_ADMIN', 'R_AUDITOR']
    }
  },
  {
    name: 'system',
    path: '/system',
    component: 'layout.base',
    meta: {
      title: 'system',
      i18nKey: 'route.system',
      icon: 'carbon:cloud-service-management',
      order: 98
    },
    children: [
      {
        name: 'system_account',
        path: '/system/account',
        component: 'view.system_account',
        meta: {
          title: 'system_account',
          i18nKey: 'route.system_account',
          icon: 'majesticons:users-line',
          order: 0,
          keepAlive: true,
          roles: ['R_ADMIN']
        }
      },
      {
        name: 'system_export',
        path: '/system/export',
        component: 'view.system_export',
        meta: {
          title: 'system_export',
          i18nKey: 'route.system_export',
          icon: 'mdi:export-variant',
          order: 22
        }
      },
      {
        name: 'system_mechanism',
        path: '/system/mechanism',
        component: 'view.system_mechanism',
        meta: {
          title: 'system_mechanism',
          i18nKey: 'route.system_mechanism',
          icon: 'mingcute:safety-certificate-line',
          order: 23,
          roles: ['R_ADMIN']
        }
      },
      {
        name: 'system_template',
        path: '/system/template',
        component: 'view.system_template',
        meta: {
          title: 'system_template',
          i18nKey: 'route.system_template',
          icon: 'icon-park-outline:page-template',
          order: 21,
          keepAlive: true,
          roles: ['R_ADMIN']
        }
      }
    ]
  }
];
